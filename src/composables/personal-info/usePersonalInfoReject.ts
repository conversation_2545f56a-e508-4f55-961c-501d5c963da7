import { useMutation } from '@vue/apollo-composable'
import { REJECT_PERSONAL_INFO_FIELD } from '@/api/graphql/mutations/participantMutations'
import { useAppStore } from '@/stores/app/appStore'

export function usePersonalInfoReject() {
    const appStore = useAppStore()

    const { mutate: rejectPersonalInfoField, loading: rejecting } = useMutation(REJECT_PERSONAL_INFO_FIELD)

    const rejectField = async (
        personalInfoId: string,
        fieldName: string,
        rejectReason: string,
        userId: string
    ) => {
        try {
            const result = await rejectPersonalInfoField({
                id: personalInfoId,
                fieldName,
                rejectReason,
                userId
            })

            appStore.showSnack('Field rejected successfully')
            return result?.data?.rejectPersonalInfoField
        } catch (error) {
            console.error('Error rejecting PersonalInfo field:', error)
            appStore.showSnack('Error rejecting field')
            throw error
        }
    }

    return {
        rejectField,
        rejecting
    }
} 