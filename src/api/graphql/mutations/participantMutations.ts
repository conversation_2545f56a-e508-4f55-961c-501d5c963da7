import gql from 'graphql-tag'

// Fragment for complete participant data
export const PARTICIPANT_FRAGMENT = gql`
    fragment ParticipantFragment on Participant {
        id
        status
        createdAt
        updatedAt
        createdBy
        updatedBy
        lastModified
        personalInfo {
            id
            firstName
            lastName
            email
            phone
            maritalStatus
            birthDay
            birthMonth
            birthYear
            certificationRejectReason {
                id
                field
                reason
                status
                createdAt
                updatedAt
                personalInfoId
            }
            address {
                id
                street
                houseNumber
                postalCode
                city
                state
                country
            }
            partnerInfo {
                id
                firstName
                lastName
                dateOfBirth
                startDate
                isCurrent
                isDeceased
            }
            children {
                id
                firstName
                lastName
                dateOfBirth
                isOrphan
                isStudying
            }
        }
        employmentInfo {
            id
            employeeId
            department
            position
            regNum
            havNum
            startDate
            endDate
            status
            salaryEntries {
                id
                year
                amount
                partTimePercentage
            }
        }
        pensionInfo {
            id
            code
            codeDescription
            codeEffectiveDate
            accruedGrossAnnualOldAgePension
            accruedGrossAnnualPartnersPension
            accruedGrossAnnualSinglesPension
            attainableGrossAnnualOldAgePension
            extraAccruedGrossAnnualOldAgePension
            extraAccruedGrossAnnualPartnersPension
            grossAnnualDisabilityPension
            pensionBase
        }
        pensionData {
            id
            status
            retirementDate
            pensionableAmount
            totalContributions
            voluntaryContributions {
                id
                amount
                accumulatedInterest
                date
                type
            }
            annualAccrual {
                id
                employeeContributions
                employerContributions
                franchise
                monthlyBenefit
            }
            conversionDetails {
                id
                conversionDate
                conversionRate
                oldAgePensionIncrease
                partnerPensionIncrease
            }
            pensionParameters {
                id
                accrualPercentage
                annualMultiplier
                offsetAmount
                partnersPensionPercentage
                retirementAge
                voluntaryContributionInterestRate
                year
                effectiveDate
            }
        }
        documents {
            id
            name
            type
            size
            uploadedAt
        }
        certifiedData {
            id
            certificationYear
            certifiedAt
            certificationStatus
        }
    }
`

export const CREATE_PARTICIPANT = gql`
    mutation CreateParticipant($input: CreateParticipantInput!) {
   createParticipant(createParticipantInput: $input) {
       id
       status
       createdAt
       updatedAt
       createdBy
       updatedBy
       lastModified
       personalInfo {
           id
           firstName
           lastName
           email
           phone
           maritalStatus
           birthDay
           birthMonth
           birthYear
           address {
               id
               street
               houseNumber
               postalCode
               city
               state
               country
           }
           partnerInfo {
               id
               firstName
               lastName
               dateOfBirth
               startDate
               isCurrent
               isDeceased
           }
           children {
               id
               firstName
               lastName
               dateOfBirth
               isOrphan
               isStudying
           }
       }
       employmentInfo {
           id
           employeeId
           department
           position
           regNum
           havNum
           startDate
           endDate
           status
           salaryEntries {
               id
               year
               amount
               partTimePercentage
           }
       }
       pensionInfo {
           id
           code
           codeDescription
           codeEffectiveDate
           accruedGrossAnnualOldAgePension
           accruedGrossAnnualPartnersPension
           accruedGrossAnnualSinglesPension
           attainableGrossAnnualOldAgePension
           extraAccruedGrossAnnualOldAgePension
           extraAccruedGrossAnnualPartnersPension
           grossAnnualDisabilityPension
           pensionBase
       }
       pensionData {
           id
           status
           retirementDate
           pensionableAmount
           totalContributions
           voluntaryContributions {
               id
               amount
               accumulatedInterest
               date
               type
           }
           annualAccrual {
               id
               employeeContributions
               employerContributions
               franchise
               monthlyBenefit
           }
           conversionDetails {
               id
               conversionDate
               conversionRate
               oldAgePensionIncrease
               partnerPensionIncrease
           }
           pensionParameters {
               id
               accrualPercentage
               annualMultiplier
               offsetAmount
               partnersPensionPercentage
               retirementAge
               voluntaryContributionInterestRate
               year
               effectiveDate
           }
       }
       certifiedData {
           id
           certificationYear
           certifiedAt
           certificationStatus
       }
   }
  }
`


// Update Participant Mutation
export const UPDATE_PARTICIPANT = gql`
    ${PARTICIPANT_FRAGMENT}
    mutation UpdateParticipant($input: UpdateParticipantInput!) {
        updateParticipant(updateParticipantInput: $input) {
            ...ParticipantFragment
        }
    }
`

// Delete Participant Mutation
export const DELETE_PARTICIPANT = gql`
    mutation DeleteParticipant($id: String!) {
        removeParticipant(id: $id) {
            id
            status
        }
    }
`

// Reject PersonalInfo Field Mutation
export const REJECT_PERSONAL_INFO_FIELD = gql`
    mutation RejectPersonalInfoField($id: ID!, $fieldName: String!, $rejectReason: String!, $userId: String!) {
        rejectPersonalInfoField(id: $id, fieldName: $fieldName, rejectReason: $rejectReason, userId: $userId) {
            id
            firstName
            lastName
            email
            phone
            maritalStatus
            birthDay
            birthMonth
            birthYear
            pendingChanges
            certificationRejectReason {
                id
                field
                reason
                status
                createdAt
                updatedAt
                personalInfoId
            }
        }
    }
`
