import { computed } from 'vue'
import { useQuery, useMutation, provideApolloClient } from '@vue/apollo-composable'
import {
  GET_ALL_PARTICIPANTS,
  GET_PARTICIPANT_BY_ID
} from '@/api/graphql/queries/participantQueries'
import {
  CREATE_PARTICIPANT,
  UPDATE_PARTICIPANT
} from '@/api/graphql/mutations/participantMutations'
import { apolloClient } from '@/api/middleware/apolloClient'
import { Participant } from '@/types/participant.types'

export const useParticipantGraph = () => {
  provideApolloClient(apolloClient)

  const router = useRouter()

  const participantId = computed(() => router.currentRoute.value.params.id as string)

  // Mutations
  const { mutate: createParticipantMutation, loading: creatingParticipant } = useMutation(CREATE_PARTICIPANT)
  const { mutate: updateParticipantMutation, loading: updatingParticipant } = useMutation(UPDATE_PARTICIPANT)

  const {
    result: allParticipantsResult,
    loading: loadingParticipants,
    refetch: refetchParticipants,
    error: participantsError
  } = useQuery(GET_ALL_PARTICIPANTS)

  const participantsList = computed<Participant[]>(() =>
    allParticipantsResult.value?.getAllParticipants || []
  )

    const { result: getParticipant, loading: loadingParticipant, refetch: refetchSingleParticipant, error: participantError } = useQuery(
      GET_PARTICIPANT_BY_ID,
      {id:  participantId.value },
      { fetchPolicy: 'network-only', enabled: !!participantId.value }
    )

    const singleParticipant = computed<Participant>(() =>
      getParticipant.value?.getParticipantById
    )

  return {
    state: {
      participantsList,
      loadingParticipants,
      participantsError,
      singleParticipant,
      loadingParticipant,
      creatingParticipant,
      updatingParticipant
    },
    actions: {
      refetchParticipants,
      refetchSingleParticipant
    },
    mutations: {
      createParticipantMutation,
      updateParticipantMutation
    }
  }
}