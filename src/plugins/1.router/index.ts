import { setupLayouts } from 'virtual:generated-layouts'
import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router/auto'
import { createRouter, createWebHistory } from 'vue-router/auto'
import { useAuthStore } from "@/stores/auth/authStore";

function recursiveLayouts(route: RouteRecordRaw): RouteRecordRaw {
  if (route.children) {
    for (let i = 0; i < route.children.length; i++)
      route.children[i] = recursiveLayouts(route.children[i])

    return route
  }

  return setupLayouts([route])[0]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to: any) {
    if (to.hash)
      return { el: to.hash, behavior: 'smooth', top: 60 }

    return { top: 0 }
  },
  extendRoutes: (pages: any) => [
    ...[...pages].map(route => recursiveLayouts(route)),
  ],
})

router.beforeEach((to: any, from: any, next: any) => {
  const authStore = useAuthStore()
  const authUid = localStorage.getItem('uid')
  const isUserLoggedIn = authStore.user != null && authStore.user.uid != null || !!authUid

  const publicRoutes = ['/login', '/register', '/forgot-password'] // Add all your public routes
  const isPublicRoute = publicRoutes.includes(to.path) || to.path.startsWith('/public/')

  if (!isUserLoggedIn && !isPublicRoute) {
    next({ path: '/login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export { router }

export default function (app: App) {
  app.use(router)
}
