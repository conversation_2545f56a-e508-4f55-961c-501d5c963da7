<template>
  <div class="add-participant-container">
    <div class="header-container">
      <div class="d-flex justify-space-between align-center">
        <div>
          <h1 class="text-h4 font-weight-bold">Add New Participant</h1>
          <p class="text-subtitle-1 text-grey-darken-1">Create a new pension participant</p>
        </div>
        <div class="d-flex gap-3">
          <v-btn
            color="primary"
            variant="outlined"
            prepend-icon="tabler-download"
            @click="exportTemplate"
            :disabled="loading"
          >
            Export Excel Template
          </v-btn>
          <v-btn
            color="success"
            variant="outlined"
            prepend-icon="tabler-upload"
            @click="triggerFileUpload"
            :disabled="loading"
          >
            Import New Participant
          </v-btn>
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            style="display: none"
            @change="handleFileUpload"
          />
        </div>
      </div>
    </div>

    <v-overlay
      :model-value="creatingParticipant"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      ></v-progress-circular>
    </v-overlay>

    <AddParticipantForm
      ref="participantFormRef"
      @submit="handleSubmit"
      @cancel="handleCancel"
      :loading="creatingParticipant"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useParticipants } from '@/composables/participants/useParticipants'
import AddParticipantForm from '@/components/participants/AddParticipantForm.vue'
import { useAppStore } from '@/stores/app/appStore'
import { exportParticipantTemplate, parseParticipantExcel, convertExcelToFormData } from '@/utils/excelUtils'

const router = useRouter()
const {
  state: { creatingParticipant, error },
  actions: { createParticipant }
} = useParticipants()
const appStore = useAppStore()
const loading = ref(false)
const fileInput = ref<HTMLInputElement>()
const participantFormRef = ref<InstanceType<typeof AddParticipantForm>>()

// Excel export function
const exportTemplate = () => {
  try {
    exportParticipantTemplate()
    appStore.showSnack('Excel template downloaded successfully')
  } catch (error) {
    console.error('Error exporting template:', error)
    appStore.showSnack('Error downloading template')
  }
}

// Trigger file input
const triggerFileUpload = () => {
  fileInput.value?.click()
}

// Handle file upload
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  loading.value = true
  try {
    const participantData = await parseParticipantExcel(file)

    if (participantData.length > 0) {
      // Convert first participant to form data and populate the form
      const formData = convertExcelToFormData(participantData[0])

      // Populate the form with imported data
      if (participantFormRef.value) {
        await participantFormRef.value.populateForm(formData)
      }

      const firstParticipant = participantData[0]
      const partnerCount = firstParticipant.personalInfo.partners.length
      const childrenCount = firstParticipant.personalInfo.children.length
      const salaryCount = firstParticipant.employmentInfo.salaryEntries.length

      let message = `Successfully imported participant data`
      if (partnerCount > 0) message += ` with ${partnerCount} partner(s)`
      if (childrenCount > 0) message += ` and ${childrenCount} child(ren)`
      if (salaryCount > 1) message += ` and ${salaryCount} salary entries`

      appStore.showSnack(message)

      if (participantData.length > 1) {
        appStore.showSnack('Note: Only the first participant data was loaded. Please create additional participants separately.')
      }
    }
  } catch (error) {
    console.error('Error importing Excel file:', error)
    appStore.showSnack(`Error importing file: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    loading.value = false
    // Reset file input
    if (target) target.value = ''
  }
}

const handleSubmit = async (formData: any) => {
  try {
    await createParticipant(formData)
    appStore.showSnack('Participant created successfully')
    router.push('/participants')
  } catch (error) {
    console.error('Error creating participant:', error)
    appStore.showSnack('Error creating participant')
  }
}

const handleCancel = () => {
  router.push('/participants')
}
</script>

<style scoped>
.add-participant-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header-container {
  margin-bottom: 32px;
}
</style> 