<script setup lang="ts">
  import { nextTick } from 'vue'
  import AppDateTimePicker from '@/@core/components/app-form-elements/AppDateTimePicker.vue'
  import { pensionCodes, getPensionCodeDescription } from '@/utils/excelUtils'

  interface Partner {
    firstName: string
    lastName: string
    dateOfBirth: string
    startDate: string
    isCurrent: boolean
    isDeceased: boolean
  }

  interface Child {
    firstName: string
    lastName: string
    dateOfBirth: string
    isOrphan: boolean
    isStudying: boolean
  }

  interface Address {
    street: string
    houseNumber: string
    postalCode: string
    city: string
    state: string
    country: string
  }

  interface PersonalInfo {
    firstName: string
    lastName: string
    email: string
    phone: string
    maritalStatus: string
    birthDate: string
    partners: Partner[]
    children: Child[]
    address: Address
  }

  interface SalaryEntry {
    year: number
    amount: number | null
    partTimePercentage: number | null
  }

  interface EmploymentInfo {
    employeeId: string
    department: string
    position: string
    startDate: string
    status: string
    salaryEntries: SalaryEntry[]
  }

  interface PensionInfo {
    code: number | null
    codeDescription: string
    codeEffectiveDate: string
    pensionBase: number | null
  }

  interface PensionCode {
    code: number
    description: string
    allowedTransitions: number[]
    impact: string
  }

  interface FormData {
    personalInfo: PersonalInfo
    employmentInfo: EmploymentInfo
    pensionInfo: PensionInfo
  }

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['submit', 'cancel'])

  const form = ref()
  const isValid = ref(false)

  const maritalStatusOptions = [
    'Single',
    'Married',
    'Divorced',
    'Widowed',
    'Separated'
  ]

  const employmentStatusOptions = [
    'Active',
    'Inactive',
    'Retired'
  ]



  const getPensionCodeIcon = (code: number): string => {
    const iconMap: Record<number, string> = {
      10: 'tabler-bolt',
      11: 'tabler-disabled',
      30: 'tabler-pause',
      40: 'tabler-rocket',
      50: 'tabler-heart',
      55: 'tabler-baby-bottle',
      70: 'tabler-cancel'
    }
    return iconMap[code] || 'tabler-user'
  }

  const getPensionCodeColor = (code: number): string => {
    const colorMap: Record<number, string> = {
      10: 'success',
      11: 'warning',
      30: 'secondary',
      40: 'primary',
      50: 'info',
      55: 'purple',
      70: 'error'
    }
    return colorMap[code] || 'grey'
  }

  const handlePensionCodeChange = (code: number) => {
    formData.value.pensionInfo.codeDescription = getPensionCodeDescription(code)
  }

  const formData = ref<FormData>({
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      maritalStatus: '',
      birthDate: '',
      partners: [],
      children: [],
      address: {
        street: '',
        houseNumber: '',
        postalCode: '',
        city: '',
        state: '',
        country: ''
      }
    },
    employmentInfo: {
      employeeId: '',
      department: '',
      position: '',
      startDate: '',
      status: 'Active',
      salaryEntries: [
        {
          year: new Date().getFullYear(),
          amount: null,
          partTimePercentage: 100
        }
      ]
    },
    pensionInfo: {
      code: null,
      codeDescription: '',
      codeEffectiveDate: '',
      pensionBase: null
    }
  })

  const isPensionCode50Or55 = computed(() => {
    const code = formData.value.pensionInfo.code
    return code === 50 || code === 55
  })

  // Watch for marital status changes
  watch(() => formData.value.personalInfo.maritalStatus, (newStatus) => {
    if (newStatus === 'Single') {
      formData.value.personalInfo.partners = []
    }
  })

  // Watch for startDate changes to update salary entry year
  watch(() => formData.value.employmentInfo.startDate, (newStartDate) => {
    if (newStartDate && formData.value.employmentInfo.salaryEntries.length > 0) {
      const startYear = new Date(newStartDate).getFullYear()
      formData.value.employmentInfo.salaryEntries[0].year = startYear
    }
  })

  const rules = {
    required: (v: any) => !!v || 'This field is required',
    email: (v: string) => {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return !v || pattern.test(v) || 'Invalid email format'
    },
    phone: (v: string) => {
      const pattern = /^\+?[\d\s-()]+$/
      return !v || pattern.test(v) || 'Invalid phone format'
    },
    positiveNumber: (v: number) => v > 0 || 'Must be a positive number',
    percentage: (v: number) => (v >= 0 && v <= 100) || 'Must be between 0 and 100'
  }

  const addPartner = () => {
    formData.value.personalInfo.partners.push({
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      startDate: '',
      isCurrent: false,
      isDeceased: false
    })
  }

  const removePartner = (index: number) => {
    formData.value.personalInfo.partners.splice(index, 1)
  }

  const addChild = () => {
    formData.value.personalInfo.children.push({
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      isOrphan: false,
      isStudying: false
    })
  }

  const removeChild = (index: number) => {
    formData.value.personalInfo.children.splice(index, 1)
  }

  // Salary entries management
  const addSalaryEntry = () => {
    const currentYear = new Date().getFullYear()
    const startYear = formData.value.employmentInfo.startDate
      ? new Date(formData.value.employmentInfo.startDate).getFullYear()
      : currentYear

    formData.value.employmentInfo.salaryEntries.push({
      year: startYear + formData.value.employmentInfo.salaryEntries.length,
      amount: null,
      partTimePercentage: 100
    })
  }

  const removeSalaryEntry = (index: number) => {
    if (formData.value.employmentInfo.salaryEntries.length > 1) {
      formData.value.employmentInfo.salaryEntries.splice(index, 1)
    }
  }

  const handleSubmit = async () => {
    const { valid } = await form.value.validate()

    if (valid) {
      try {
        emit('submit', formData.value)
      } finally {
        // Reset form or handle errors
      }
    }
  }

  // Method to populate form from imported data
  const populateForm = async (importedData: any) => {
    // Populate personal info including multiple partners and children
    if (importedData.personalInfo) {
      formData.value.personalInfo = {
        ...formData.value.personalInfo,
        ...importedData.personalInfo
      }
    }

    // Populate employment info including multiple salary entries
    if (importedData.employmentInfo) {
      formData.value.employmentInfo = {
        ...formData.value.employmentInfo,
        ...importedData.employmentInfo
      }
    }

    // Populate pension info and ensure description is set
    if (importedData.pensionInfo) {
      formData.value.pensionInfo = {
        ...formData.value.pensionInfo,
        ...importedData.pensionInfo
      }

      // Ensure pension code description is set
      if (formData.value.pensionInfo.code && !formData.value.pensionInfo.codeDescription) {
        formData.value.pensionInfo.codeDescription = getPensionCodeDescription(formData.value.pensionInfo.code)
      }
    }

    // Trigger form validation after populating data
    await nextTick() // Wait for DOM updates
    if (form.value) {
      await form.value.validate()
    }
  }

  // Expose methods for parent component
  defineExpose({
    populateForm
  })
</script>

<template>
  <v-form ref="form" v-model="isValid" @submit.prevent="handleSubmit">
    <v-row>
      <v-col cols="12">
        <v-card class="mb-6">
          <v-card-title class="text-h6">Pension Information</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-select
                  v-model="formData.pensionInfo.code"
                  label="Pension Code"
                  :items="pensionCodes"
                  item-title="code"
                  item-value="code"
                  variant="outlined"
                  density="compact"
                  :rules="[rules.required]"
                  @update:model-value="handlePensionCodeChange"
                >
                  <template v-slot:item="{ props, item }">
                    <v-list-item
                      v-bind="props"
                      :title="`${item.raw.code} - ${item.raw.description}`"
                      :subtitle="item.raw.impact"
                    >
                      <template v-slot:prepend>
                        <v-icon :icon="getPensionCodeIcon(item.raw.code)" :color="getPensionCodeColor(item.raw.code)" />
                      </template>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.pensionInfo.codeDescription"
                  label="Code Description"
                  variant="outlined"
                  density="compact"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <AppDateTimePicker
                  v-model="formData.pensionInfo.codeEffectiveDate"
                  label="Code Effective Date"
                  density="compact"
                  :rules="[rules.required]"
                  :config="{ maxDate: 'today' }"
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- Basic Information Section -->
      <v-col cols="12">
        <v-card class="mb-6">
          <v-card-title class="text-h6">Basic Information</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.firstName"
                  label="First Name"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.lastName"
                  label="Last Name"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.email"
                  label="Email"
                  :rules="[rules.required, rules.email]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.phone"
                  label="Phone"
                  :rules="[rules.phone]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="formData.personalInfo.maritalStatus"
                  label="Marital Status"
                  :items="maritalStatusOptions"
                  variant="outlined"
                  density="compact"
                ></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <AppDateTimePicker
                  v-model="formData.personalInfo.birthDate"
                  label="Date of birth"
                  :rules="[rules.required]"
                  :config="{ maxDate: 'today' }"
                  density="compact"
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Partners Section -->
      <v-col cols="12" v-if="formData.personalInfo.maritalStatus !== 'Single' && !isPensionCode50Or55">
        <v-card class="mb-6">
          <v-card-title class="d-flex justify-space-between align-center">
            <span class="text-h6">Partners</span>
            <v-btn
              color="primary"
              variant="tonal"
              size="small"
              prepend-icon="tabler-plus"
              @click="addPartner"
            >
              Add Partner
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-row v-for="(partner, index) in formData.personalInfo.partners" :key="index">
              <v-col cols="12">
                <v-card variant="outlined" class="mb-4">
                  <v-card-text>
                    <div class="d-flex justify-space-between align-center mb-4">
                      <span class="text-subtitle-1">Partner {{ index + 1 }}</span>
                      <v-btn
                        v-if="formData.personalInfo.partners.length > 1"
                        color="error"
                        variant="text"
                        icon="tabler-trash"
                        @click="removePartner(index)"
                      ></v-btn>
                    </div>
                    <v-row>
                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="partner.firstName"
                          label="First Name"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="partner.lastName"
                          label="Last Name"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6">
                        <AppDateTimePicker
                          v-model="partner.dateOfBirth"
                          label="Date of Birth"
                          :rules="[rules.required]"
                          :config="{ maxDate: 'today' }"
                          density="compact"
                        />
                      </v-col>
                      <v-col cols="12" md="6">
                        <AppDateTimePicker
                          v-model="partner.startDate"
                          label="Start Date"
                          :rules="[rules.required]"
                          density="compact"
                        />
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-checkbox
                          v-model="partner.isCurrent"
                          label="Current Partner"
                        ></v-checkbox>
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-checkbox
                          v-model="partner.isDeceased"
                          label="Deceased"
                        ></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Children Section -->
      <v-col cols="12" v-if="!isPensionCode50Or55">
        <v-card class="mb-6">
          <v-card-title class="d-flex justify-space-between align-center">
            <span class="text-h6">Children</span>
            <v-btn
              color="primary"
              size="small"
              variant="tonal"
              prepend-icon="tabler-plus"
              @click="addChild"
            >
              Add Child
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-row v-for="(child, index) in formData.personalInfo.children" :key="index">
              <v-col cols="12">
                <v-card variant="outlined" class="mb-4">
                  <v-card-text>
                    <div class="d-flex justify-space-between align-center mb-4">
                      <span class="text-subtitle-1">Child {{ index + 1 }}</span>
                      <v-btn
                        v-if="formData.personalInfo.children.length > 1"
                        color="error"
                        variant="text"
                        icon="tabler-trash"
                        @click="removeChild(index)"
                      ></v-btn>
                    </div>
                    <v-row>
                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="child.firstName"
                          label="First Name"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="child.lastName"
                          label="Last Name"
                          :rules="[rules.required]"
                          variant="outlined"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6">
                        <AppDateTimePicker
                          v-model="child.dateOfBirth"
                          label="Date of Birth"
                          :rules="[rules.required]"
                          :config="{ maxDate: 'today' }"
                          density="compact"
                        />
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-checkbox
                          v-model="child.isOrphan"
                          label="Orphan"
                        ></v-checkbox>
                      </v-col>
                      <v-col cols="12" md="6">
                        <v-checkbox
                          v-model="child.isStudying"
                          label="Currently Studying"
                        ></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Address Section -->
      <v-col cols="12">
        <v-card class="mb-6">
          <v-card-title class="text-h6">Address</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.street"
                  label="Street"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.houseNumber"
                  label="House Number"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.postalCode"
                  label="Postal Code"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.city"
                  label="City"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.state"
                  label="State"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.personalInfo.address.country"
                  label="Country"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Employment Information Section -->
      <v-col cols="12" v-if="!isPensionCode50Or55">
        <v-card class="mb-6">
          <v-card-title class="text-h6">Employment Information</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.employmentInfo.employeeId"
                  label="Employee ID"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.employmentInfo.department"
                  label="Department"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.employmentInfo.position"
                  label="Position"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <AppDateTimePicker
                  v-model="formData.employmentInfo.startDate"
                  label="Start Date"
                  :rules="[rules.required]"
                  density="compact"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="formData.employmentInfo.status"
                  label="Status"
                  :items="employmentStatusOptions"
                  :rules="[rules.required]"
                  variant="outlined"
                  density="compact"
                ></v-select>
              </v-col>
            </v-row>

            <!-- Salary Entries Section -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title class="text-subtitle-1 d-flex align-center justify-space-between">
                    <span>Salary Entries</span>
                    <v-btn
                      size="small"
                      color="primary"
                      variant="outlined"
                      @click="addSalaryEntry"
                      prepend-icon="tabler-plus"
                    >
                      Add Entry
                    </v-btn>
                  </v-card-title>
                  <v-card-text>
                    <div v-for="(entry, index) in formData.employmentInfo.salaryEntries" :key="index" class="mb-4">
                      <v-row>
                        <v-col cols="12" md="4">
                          <v-text-field
                            v-model.number="entry.year"
                            label="Year"
                            type="number"
                            :rules="[rules.required, rules.positiveNumber]"
                            variant="outlined"
                            density="compact"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4">
                          <v-text-field
                            v-model.number="entry.amount"
                            label="Salary Amount"
                            type="number"
                            :rules="[rules.required, rules.positiveNumber]"
                            variant="outlined"
                            density="compact"
                            prefix="Afl."
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3">
                          <v-text-field
                            v-model.number="entry.partTimePercentage"
                            label="Part-time %"
                            type="number"
                            :rules="[rules.required, rules.percentage]"
                            variant="outlined"
                            density="compact"
                            suffix="%"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="1" class="d-flex align-center">
                          <v-btn
                            v-if="formData.employmentInfo.salaryEntries.length > 1"
                            icon
                            size="small"
                            color="error"
                            variant="text"
                            @click="removeSalaryEntry(index)"
                          >
                            <VIcon icon="tabler-trash" />
                          </v-btn>
                        </v-col>
                      </v-row>
                      <v-divider v-if="index < formData.employmentInfo.salaryEntries.length - 1" class="mt-2" />
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Form Actions -->
      <v-col cols="12" class="d-flex justify-end gap-4">
        <v-btn
          variant="outlined"
          color="primary"
          @click="$emit('cancel')"
          :disabled="props.loading"
        >
          Cancel
        </v-btn>
        <v-btn
          type="submit"
          color="primary"
          :loading="props.loading"
          :disabled="!isValid || props.loading"
        >
          Create Participant
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<style scoped>
.v-row {
  margin: 0;
}
</style>