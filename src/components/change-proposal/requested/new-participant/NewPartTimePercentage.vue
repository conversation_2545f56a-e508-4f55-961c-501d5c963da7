
<script setup lang="ts">
  import { ChangeType, SalaryEntry } from '@/gql/graphql'
  import { useAppStore } from '@/stores/app/appStore'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import {formatPercentage} from '@/utils/transformers'

  const props = defineProps({
    entries: {
      type: Array as () => SalaryEntry[],
      required: true
    },
    participantName: {
      type: String,
      required: true
    },
    employmentInfoId: {
      type: String,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  })

  const emit = defineEmits(['refresh'])

  const dialogVisible = ref(false)
  const selectedEntry = ref<SalaryEntry | null>(null)
  const currentYear = new Date().getFullYear()

  const appStore = useAppStore()

  const pensionStore = usePensionStore()

  const certifiedYears = computed(() => {
    return pensionStore.certifiedDataYears
  })

  const isDisabled =  (pendingChanges: string[])=>{
    return pendingChanges.includes('partTimePercentage')
  }

  const isCurrentYear = (year: number): boolean => {
    return year === currentYear
  }

  const isCertifiedYear = (year: number) => {
    return certifiedYears.value.includes(year)
  }

  const getStatusColor = (year: number): string => {
    if (isCurrentYear(year)) return 'primary'
    return 'success'
  }

  const openEditDialog = (entry: SalaryEntry) => {
    if(isDisabled(entry?.pendingChanges)) {
      appStore.showSnack('Sorry you cannot edit this field.')
      return
    }
    selectedEntry.value = entry
    dialogVisible.value = true
  }
</script>


<template>
  <v-card variant="outlined" class="mb-4">
    <v-card-title class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">Part-time Percentage</h4>
    </v-card-title>
    <v-card-subtitle class="text-caption text-medium-emphasis">
      Percentage as of January 1st or start date if started during the year
    </v-card-subtitle>
    <v-card-text>
      <v-table class="percentage-table" density="comfortable">
        <thead>
          <tr>
            <th class="text-left">YEAR</th>
            <th class="text-left">PART-TIME %</th>
            <th class="text-center">STATUS</th>
            <th v-if="canEdit" class="text-center">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="4" class="text-center">
              <v-progress-circular indeterminate color="primary" />
            </td>
          </tr>
          <tr v-else-if="entries.length === 0">
            <td colspan="4" class="text-center">No part-time percentage data found</td>
          </tr>
          <tr v-for="entry in entries" :key="entry.id" :class="isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : ''">
            <td class="font-weight-medium">{{ entry.year }}</td>
            <td>{{ formatPercentage(entry.partTimePercentage) }}</td>
            <td class="text-center">
              <v-chip :color="getStatusColor(entry.year)" size="small" label>
                {{ isCurrentYear(entry.year) ? 'Current' : 'Certified' }}
              </v-chip>
            </td>
            <td v-if="canEdit" class="text-center">
              <v-btn
                v-if="!isCertifiedYear(entry.year)"
                icon
                size="small"
                variant="text"
                color="primary"
                @click="openEditDialog(entry)"
              >
                <VIcon
                  v-if="isDisabled(entry?.pendingChanges)"
                  size="16"
                  icon="tabler-alert-triangle"
                  class="edit-icon"
                  color="error"
                />
                <VIcon
                  v-else
                  size="16"
                  icon="tabler-edit"
                  class="edit-icon"
                  color="primary"
                />
              </v-btn>
            </td>
          </tr>
        </tbody>
      </v-table>
    </v-card-text>

    <SalaryEntityDialog
      :editMode="true"
      v-model="dialogVisible"
      :path="`partTimePercentage`"
      :entityId="selectedEntry?.id"
      :entityType="`SalaryEntry`"
      :entry="selectedEntry as any"
      formType="partTime"
      :participantName="participantName"
      :type="ChangeType.Participant"
      :year="selectedEntry?.year"
      @refresh="$emit('refresh')"
    />
  </v-card>
</template>

<style scoped>
  .percentage-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .percentage-table th {
    font-size: 0.75rem;
    letter-spacing: 0.0625rem;
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }
</style>
