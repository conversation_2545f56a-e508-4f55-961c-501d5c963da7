<script setup lang="ts">
  import { SalaryEntry } from '@/gql/graphql'
  import PensionPrimaryCalc from '@/components/participants/salaryPension/PensionPrimaryCalc.vue'
  import { select } from 'cheerio-select'

  interface SalaryItem {
    id: string
    year: string
    grossSalary: string
    status: string
  }

  interface PercentageItem {
    id: string
    year: string
    percentage: string
    status: string
  }

  interface PensionItem {
    id: string
    year: string
    grossMonthlySalary: string
    annualMultiplier: string
    grossAnnualSalary: string
    offset: string
  }

  const props = defineProps({
    salaryData: {
      type: Array as () => SalaryItem[],
      default: () => [
        {
          id: 'sal-2024',
          year: '2024',
          grossSalary: 'Afl. 4.680,00',
          status: 'Current'
        },
        {
          id: 'sal-2023',
          year: '2023',
          grossSalary: 'Afl. 4.555,00',
          status: 'Certified'
        },
        {
          id: 'sal-2022',
          year: '2022',
          grossSalary: 'Afl. 4.430,00',
          status: 'Certified'
        }
      ]
    },
    percentageData: {
      type: Array as () => PercentageItem[],
      default: () => [
        {
          id: 'pct-2024',
          year: '2024',
          percentage: '100.00%',
          status: 'Current'
        },
        {
          id: 'pct-2023',
          year: '2023',
          percentage: '100.00%',
          status: 'Certified'
        },
        {
          id: 'pct-2022',
          year: '2022',
          percentage: '100.00%',
          status: 'Certified'
        }
      ]
    },
    pensionData: {
      type: Array as () => PensionItem[],
      default: () => [
        {
          id: 'pen-2024',
          year: '2024',
          grossMonthlySalary: 'Afl. 4.680,00',
          annualMultiplier: '13',
          grossAnnualSalary: 'Afl. 60.840,00',
          offset: 'Afl. 17.616,00'
        },
        {
          id: 'pen-2023',
          year: '2023',
          grossMonthlySalary: 'Afl. 4.555,00',
          annualMultiplier: '13',
          grossAnnualSalary: 'Afl. 59.215,00',
          offset: 'Afl. 17.616,00'
        },
        {
          id: 'pen-2022',
          year: '2022',
          grossMonthlySalary: 'Afl. 4.430,00',
          annualMultiplier: '13',
          grossAnnualSalary: 'Afl. 57.590,00',
          offset: 'Afl. 17.616,00'
        }
      ]
    },
    participant: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:salaryData', 'update:percentageData', 'update:pensionData']);


  const dialogVisible = ref(false)
  const selectedEntry = ref<SalaryEntry | null>(null)

  const editSalaryDialog = ref(false);
  const editPercentageDialog = ref(false);
  const editPensionDialog = ref(false);
  const selectedSalary = ref({ year: '', grossSalary: '', status: '', id: '' });
  const selectedPercentage = ref({ year: '', percentage: '', status: '', id: '' });
  const selectedPension = ref({
    year: '',
    grossMonthlySalary: '',
    annualMultiplier: '',
    grossAnnualSalary: '',
    offset: '',
    id: ''
  });

  const openSalaryEditDialog = (item: any) => {
    selectedSalary.value = { ...item };
    editSalaryDialog.value = true;
  };

  const openAddDialog = () => {
    selectedEntry.value = null
    dialogVisible.value = true
  }

  const openPercentageEditDialog = (item: any) => {
    selectedPercentage.value = { ...item };
    editPercentageDialog.value = true;
  };


  const isCollapsed = ref(false);
  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
  };

  // Helper function to determine status color
  const getStatusColor = (status: string) => {
    if (status === 'Current') return 'primary';
    if (status === 'Certified') return 'success';
    return 'default';
  };

  const salaryEntries = ref<SalaryEntry[]>([])
  const loadingSalaryEntries = ref(false)

  const fetchSalaryEntries = () => {
    if (!props.participant || !props.participant.employmentInfo) return

    loadingSalaryEntries.value = true

    if (props.participant.employmentInfo.salaryEntries) {
     salaryEntries.value = [...props.participant.employmentInfo.salaryEntries].sort((a, b) => b.year - a.year)
    } else {
      salaryEntries.value = []
    }

    loadingSalaryEntries.value = false
  }

  onMounted(() => {
    if (props.participant) {
      fetchSalaryEntries()
    }
  })

  watch(() => props.participant, (newVal) => {
    if (newVal) {
      fetchSalaryEntries()
    }
  }, { deep: true })
</script>

<template>
  <v-card class="salary-pension-container" elevation="0">
    <v-card-title class="header-container d-flex justify-space-between align-center">
      <h2 class="text-h4 font-weight-medium">Salary & Pension Base</h2>
      <v-btn
        variant="outlined"
        color="primary"
        size="small"
        @click="toggleCollapse"
        :prepend-icon="isCollapsed ? 'tabler-chevron-down' : 'tabler-chevron-up'"
      >
        {{ isCollapsed ? 'Expand' : 'Collapse' }}
      </v-btn>
    </v-card-title>

    <v-card-text v-if="!isCollapsed">
      <!-- Salary Information Section -->
      <div class="section-container mb-6">
        <div class="section-header d-flex justify-space-between align-start mb-4">
          <div>
            <h3 class="text-h6 font-weight-medium">Salary Information</h3>
            <div class="text-caption text-medium-emphasis">Overview of annual salary and part-time percentage</div>
          </div>
          <div class="d-flex justify-end mb-3">
            <v-btn
              color="primary"
              size="small"
              @click="openAddDialog"
              prepend-icon="tabler-plus"
            >
              Add New Entry
            </v-btn>
          </div>
        </div>

        <v-row>
          <v-col cols="12" md="6" class="pr-md-3">
            <NewSalaryEntries
              v-if="participant && participant.employmentInfo"
              :participant-id="participant.id"
              :employment-info-id="participant.employmentInfo.id"
              :can-edit="true"
              @refresh="fetchSalaryEntries"
            />
            <v-card v-else variant="outlined" class="mb-4">
              <v-card-title class="py-3">
                <h4 class="text-subtitle-1 font-weight-medium">Gross Part-time Monthly Salary</h4>
              </v-card-title>
              <v-card-subtitle class="text-caption text-medium-emphasis">
                Amount as of January 1st or start date if started during the year
              </v-card-subtitle>
              <v-card-text>
                <v-table class="salary-table" density="comfortable">
                  <thead>
                  <tr>
                    <th class="text-left">YEAR</th>
                    <th class="text-left">GROSS PART-TIME MONTHLY SALARY</th>
                    <th class="text-center">STATUS</th>
                    <th class="text-center">ACTIONS</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="item in salaryData" :key="item.id" :class="item.status === 'Current' ? 'bg-blue-lighten-5' : ''">
                    <td class="font-weight-medium">{{ item.year }}</td>
                    <td>{{ item.grossSalary }}</td>
                    <td class="text-center">
                      <v-chip :color="getStatusColor(item.status)" size="small" label>
                        {{ item.status }}
                      </v-chip>
                    </td>
                    <td class="text-center">
                      <VBtn
                        v-if="item.status === 'Current'"
                        icon
                        size="small"
                        variant="text"
                        color="primary"
                        @click="openSalaryEditDialog(item)"
                      >
                        <v-icon>tabler-edit</v-icon>
                      </VBtn>
                    </td>
                  </tr>
                  </tbody>
                </v-table>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6" class="pl-md-3">
            <NewPartTimePercentage
              v-if="participant && participant.employmentInfo && salaryEntries.length > 0"
              :entries="salaryEntries"
              :participantName="participant?.personalInfo?.firstName + ' '+ participant?.personalInfo?.lastName"
              :employment-info-id="participant.employmentInfo.id"
              :loading="loadingSalaryEntries"
              :can-edit="true"
              @refresh="fetchSalaryEntries"
            />
            <v-card v-else variant="outlined" class="mb-4">
              <v-card-title class="py-3">
                <h4 class="text-subtitle-1 font-weight-medium">Part-time Percentage</h4>
              </v-card-title>
              <v-card-subtitle class="text-caption text-medium-emphasis">
                Percentage as of January 1st or start date if started during the year
              </v-card-subtitle>
              <v-card-text>
                <v-table class="percentage-table" density="comfortable">
                  <thead>
                  <tr>
                    <th class="text-left">YEAR</th>
                    <th class="text-left">PART-TIME %</th>
                    <th class="text-center">STATUS</th>
                    <th class="text-center">ACTIONS</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="item in percentageData" :key="item.id" :class="item.status === 'Current' ? 'bg-blue-lighten-5' : ''">
                    <td class="font-weight-medium">{{ item.year }}</td>
                    <td>{{ item.percentage }}</td>
                    <td class="text-center">
                      <v-chip :color="getStatusColor(item.status)" size="small" label>
                        {{ item.status }}
                      </v-chip>
                    </td>
                    <td class="text-center">

                      <VBtn
                        v-if="item.status === 'Current'"
                        icon
                        size="small"
                        variant="text"
                        color="primary"
                        @click="openPercentageEditDialog(item)"
                      >
                        <v-icon>tabler-edit</v-icon>
                      </VBtn>
                    </td>
                  </tr>
                  </tbody>
                </v-table>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>

      <v-divider class="my-6" />

      <!-- Pension Base Calculation Section -->
      <PensionPrimaryCalc />
    </v-card-text>

    <SalaryEntityDialog
      :editMode="false"
      v-model="dialogVisible"
      path="new"
      entityId=""
      :employment-info-id="participant.employmentInfo.id"
      entityType="salaryEntry"
      :entry="selectedEntry as any"
      formType="new"
      :year="2026"
      @refresh="$emit('refresh')"
    />
  </v-card>
</template>

<style scoped>
  .salary-pension-container {
    border-radius: 8px;
  }

  .header-container {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 20px
  }

  .section-header {
    margin-bottom: 16px;
  }

  .salary-table,
  .percentage-table,
  .pension-table {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
  }

  .salary-table th,
  .percentage-table th,
  .pension-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }

  @media (max-width: 960px) {
    .pl-md-3, .pr-md-3 {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
</style>