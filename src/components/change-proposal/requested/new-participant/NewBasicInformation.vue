<script setup lang="ts">
  import { computed, ref } from 'vue'
  import avatar from '@/assets/images/avatars/avatar-0.png'
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useAppStore } from '@/stores/app/appStore'

  interface FieldItem {
    __typename?: string;
    typename?: string;
    id: string;
    name: string;
    field: string;
    value: any;
    disabled: boolean;
    changeRequested?: boolean;
    approvedChanges?: boolean;
  }

  const props = defineProps({
    showChangeTracking: {
      type: Boolean,
      default: true
    }
  });

  const {
    state: { participantPersonalInfo, loadingParticipant }
  } = useParticipants();
  const appStore = useAppStore();

  // Get rejected fields based on disabled status (if change tracking is enabled)
  const rejectedFields = computed(() => {
    if (!props.showChangeTracking) return [];

    return processedParticipantData.value
      .filter(field => field.disabled)
      .map(field => ({
        field: field.name,
        fieldKey: field.field
      }));
  });

  function fieldKeyToName(fieldKey: string): string {
    if (!fieldKey) return '';
    const result = fieldKey.replace(/([A-Z])/g, ' $1');
    return result.charAt(0).toUpperCase() + result.slice(1).trim();
  }

  const processedParticipantData = computed((): FieldItem[] => {
    const personalInfo = participantPersonalInfo.value;

    if (!personalInfo) return [];

    const transformed: FieldItem[] = [];
    const relevantFields: (keyof typeof personalInfo)[] = ['firstName', 'lastName', 'email', 'phone', 'maritalStatus'];
    const pendingChanges = personalInfo.pendingChanges || [];
    const requestedChanges = personalInfo.requestedChanges || [];
    const approvedChanges = personalInfo.approvedChanges || [];

    relevantFields.forEach(key => {
      if (Object.prototype.hasOwnProperty.call(personalInfo, key) &&
        personalInfo[key] !== undefined &&
        personalInfo[key] !== null) {
        transformed.push({
          typename: 'PersonalInfo',
          id: personalInfo.id,
          name: fieldKeyToName(key as string),
          field: key as string,
          value: personalInfo[key],
          disabled: pendingChanges.includes(key),
          changeRequested: props.showChangeTracking ? requestedChanges.includes(key) : false,
          approvedChanges: props.showChangeTracking ? approvedChanges.includes(key) : false,
          rejectReason: props.showChangeTracking ?
            personalInfo.certificationRejectReason?.find((item: any) => item.field === key)?.reason : undefined
        });
      }
    });

    // Handle birth date composition
    if (personalInfo.birthYear && personalInfo.birthMonth && personalInfo.birthDay) {
      try {
        const date = new Date(personalInfo.birthYear, personalInfo.birthMonth - 1, personalInfo.birthDay);
        const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
        transformed.push({
          typename: 'PersonalInfo',
          id: personalInfo.id,
          name: 'Birth Date',
          field: 'birthDate',
          value: date.toLocaleDateString(undefined, options),
          disabled: pendingChanges.includes('birthDate'),
          changeRequested: props.showChangeTracking ? requestedChanges.includes('birthDate') : false,
          approvedChanges: props.showChangeTracking ? approvedChanges.includes('birthDate') : false,
          rejectReason: props.showChangeTracking ?
            personalInfo.certificationRejectReason?.find((item: any) => item.field === 'birthDate')?.reason : undefined
        });
      } catch (e) {
        console.error("Error formatting birth date:", e);
      }
    }

    return transformed;
  });

  const fieldsToExclude = [
    'pendingChanges', 'birthDay', 'birthMonth', 'birthYear',
    'id', 'participantId', 'partnerInfo', 'children', 'personalInfo', 'address',
    'requestedChanges', 'approvedChanges', 'certificationRejectReason', '__typename'
  ];

  const displayableFields = computed(() => {
    return processedParticipantData.value.filter(item => !fieldsToExclude.includes(item.field));
  });

  const fullName = computed(() => {
    const info = participantPersonalInfo.value;
    if (info) return `${info.firstName || ''} ${info.lastName || ''}`.trim();
    return 'N/A';
  });

  const getIconForField = (fieldName: string): string => {
    const iconMap: Record<string, string> = {
      phone: 'tabler-phone',
      birthDate: 'tabler-calendar-event',
      firstName: 'tabler-user-circle',
      lastName: 'tabler-user-circle',
      email: 'tabler-mail',
      maritalStatus: 'tabler-heart',
      address: 'tabler-home',
    };
    return iconMap[fieldName] || 'tabler-info-circle';
  };

  const formatFieldValue = (field: FieldItem): string => {
    if (field.field.toLowerCase().includes('date') && field.value) {
      try {
        return new Date(field.value).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
      } catch (e) {
        return String(field.value);
      }
    }
    return String(field.value ?? '');
  };

  const showApprovalDialog = ref(false);
  const fieldToApprove = ref<FieldItem | null>(null);

  const showRevertDialog = ref(false);
  const fieldToRevert = ref<FieldItem | null>(null);

  // Determine chip color based on field state (only if change tracking is enabled)
  const getChipColor = (field: FieldItem): string => {
    if (!props.showChangeTracking) return 'default';

    if (field.approvedChanges) return 'success';
    if (field.changeRequested) return 'primary';
    return 'default';
  };

  // Determine chip icon based on field state (only if change tracking is enabled)
  const getChipIcon = (field: FieldItem): string => {
    if (!props.showChangeTracking) return '';

    if (field.approvedChanges) return 'tabler-check';
    if (field.changeRequested) return 'tabler-clock';
    return '';
  };

  // Get tooltip text for chips (only if change tracking is enabled)
  const getChipTooltip = (field: FieldItem): string => {
    if (!props.showChangeTracking) return '';

    if (field.approvedChanges) return 'Changes approved';
    if (field.changeRequested) return 'Changes requested';
    return '';
  };

  // Determine if action button should be shown (only if change tracking is enabled)
  const shouldShowActionButton = (field: FieldItem): boolean => {
    if (!props.showChangeTracking) return false;
    // Show approval/reject button for all fields when change tracking is enabled
    return true;
  };

  // Determine if action button should be disabled
  const isActionButtonDisabled = (field: FieldItem): boolean => {
    // Disable if there are already change requests or approved changes
    return Boolean(field.changeRequested || field.approvedChanges);
  };

  // Get action button color
  const getActionButtonColor = (field: FieldItem): string => {
    if (field.changeRequested || field.approvedChanges) return 'grey';
    return 'warning';
  };

  // Get action button icon
  const getActionButtonIcon = (field: FieldItem): string => {
    if (field.changeRequested) return 'tabler-clock';
    if (field.approvedChanges) return 'tabler-check';
    return 'tabler-gavel';
  };

  // Get action button tooltip
  const getActionButtonTooltip = (field: FieldItem): string => {
    if (field.changeRequested) return 'Change request pending';
    if (field.approvedChanges) return 'Changes already approved';
    return 'Review and approve/reject changes';
  };

  // Handle action button click
  const handleActionClick = (field: FieldItem) => {
    if (isActionButtonDisabled(field)) return;

    console.log('Action button clicked for field:', field);

    // Open approval dialog for any field that's not already in process
    if (!field.changeRequested && !field.approvedChanges) {
      fieldToApprove.value = field;
      showApprovalDialog.value = true;
    }
  };

  // Get action button menu items (only if change tracking is enabled)
  const getActionMenuItems = (field: FieldItem) => {
    if (!props.showChangeTracking) return [];

    const items = [];

    // Always show approve/reject option if not already processed
    if (!field.changeRequested && !field.approvedChanges) {
      items.push({
        title: 'Approve/Reject',
        icon: 'tabler-gavel',
        action: () => {
          fieldToApprove.value = field;
          showApprovalDialog.value = true;
        }
      });
    }

    // Show revert option if there are pending or approved changes
    if (field.changeRequested || field.approvedChanges) {
      items.push({
        title: 'Revert Changes',
        icon: 'tabler-rotate-clockwise',
        action: () => {
          fieldToRevert.value = field;
          showRevertDialog.value = true;
        }
      });
    }

    return items;
  };
</script>

<template>
  <v-skeleton-loader
    v-if="loadingParticipant"
    class="mx-auto border py-6"
    type="list-item-avatar-three-line"
  />

  <VCard v-else class="basic-information-container">
    <VCardText>
      <!-- Simple alerts for rejected fields (disabled fields) -->
      <div v-if="rejectedFields.length > 0 && showChangeTracking" class="mb-4">
        <VAlert
          v-for="(item, index) in rejectedFields"
          :key="`rejected-${item.fieldKey}-${index}`"
          type="info"
          variant="tonal"
          class="mb-2"
          closable
        >
          <template #prepend>
            <VIcon
              icon="tabler-info-circle"
              size="10"
            />
          </template>
          <div class="d-flex align-center">
            <strong class="mr-2">{{ item.field }}:</strong>
            <span>Field has been rejected and is pending review</span>
            <VChip
              size="small"
              color="primary"
              class="ml-auto"
            >
              Follow-up submitted
            </VChip>
          </div>
        </VAlert>
      </div>

      <div class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5">
        <div class="d-flex">
          <VAvatar
            size="100"
            :image="avatar"
            class="mx-auto my-auto"
          />
        </div>

        <div class="w-100 mt-8 pt-4 mt-sm-0">
          <h6 class="text-h4 text-center text-sm-start font-weight-medium mb-3">
            {{ fullName }}
          </h6>

          <div class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4">
            <div class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4">
              <span
                v-for="field in displayableFields"
                :key="field.field"
                class="d-flex align-center"
              >
                <VIcon size="20" :icon="getIconForField(field.field)" class="me-1" />

                <span class="text-body-1 mr-1">
                  <template v-if="field.field === 'status'">
                    <VChip
                      class="ml-2 font-weight-bold"
                      label
                      :color="getChipColor(field)"
                      density="compact"
                    >
                      {{ formatFieldValue(field) }}
                    </VChip>
                  </template>

                  <template v-else-if="showChangeTracking && (field.changeRequested || field.approvedChanges)">
                    <v-chip
                      :model-value="true"
                      class="ma-2"
                      size="small"
                      :color="getChipColor(field)"
                      :prepend-icon="getChipIcon(field)"
                    >
                      {{ formatFieldValue(field) }}
                      <v-tooltip activator="parent" location="top">
                        {{ getChipTooltip(field) }}
                      </v-tooltip>
                    </v-chip>
                  </template>

                  <template v-else>
                    {{ formatFieldValue(field) }}
                  </template>
                </span>



                <!-- Action Button for fields that need management (only if change tracking is enabled) -->
                <v-menu v-if="shouldShowActionButton(field)">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon
                      size="small"
                      variant="text"
                      :color="getActionButtonColor(field)"
                      v-bind="props"
                      :disabled="isActionButtonDisabled(field)"
                    >
                      <v-icon :icon="getActionButtonIcon(field)" />
                      <v-tooltip activator="parent" location="top">
                        {{ getActionButtonTooltip(field) }}
                      </v-tooltip>
                    </v-btn>
                  </template>

                  <v-list>
                    <v-list-item
                      v-for="menuItem in getActionMenuItems(field)"
                      :key="menuItem.title"
                      @click="menuItem.action"
                    >
                      <template v-slot:prepend>
                        <v-icon :icon="menuItem.icon" />
                      </template>
                      <v-list-item-title>{{ menuItem.title }}</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </span>

              <div v-if="displayableFields.length === 0 && processedParticipantData.length > 0" class="text-caption text-disabled pa-2">
                No details to display based on current filters.
              </div>
              <div v-if="!loadingParticipant && processedParticipantData.length === 0" class="text-caption text-disabled pa-2">
                No participant details available.
              </div>
            </div>
          </div>
        </div>
      </div>
    </VCardText>



    <!-- Change tracking dialogs (only if change tracking is enabled) -->
    <template v-if="showChangeTracking">
      <ApproveRejectFields
        v-model="showApprovalDialog"
        :field="fieldToApprove"
        entity-type="personalInfo"
        :participant-name="fullName"
        @close="showApprovalDialog = false"
      />

      <RevertApproveRejectFields
        v-model="showRevertDialog"
        :field="fieldToRevert"
        entity-type="personalInfo"
        :participant-name="fullName"
        @close="showRevertDialog = false"
      />
    </template>
  </VCard>
</template>

<style scoped>
  .basic-information-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: white;
  }

  .info-item {
    position: relative;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .info-item.cursor-pointer:hover {
    background-color: rgba(var(--v-theme-primary), 0.08);
  }

  .edit-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .info-item.cursor-pointer:hover .edit-icon,
  span.cursor-pointer:hover .edit-icon {
    opacity: 1;
  }

  .field-container {
    position: relative;
    padding: 2px 4px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }

  .field-container:hover {
    background-color: rgba(var(--v-theme-surface), 0.04);
  }

  span.cursor-pointer:hover {
    background-color: rgba(var(--v-theme-primary), 0.08);
    border-radius: 6px;
    padding: 2px 4px;
  }
</style>